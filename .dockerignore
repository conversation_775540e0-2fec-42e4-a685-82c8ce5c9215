# =====================================================
# AGENTSALUD MVP - DOCKERIGNORE FOR COOLIFY
# =====================================================
# Optimized .dockerignore for production builds
# 
# <AUTHOR> DevOps Team
# @date January 2025

# Dependencies
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Next.js build output
.next/
out/

# Production build
build/
dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
.gitattributes

# Documentation
README.md
CHANGELOG.md
LICENSE
*.md

# Testing
coverage/
.nyc_output
.jest/
test-results/
playwright-report/

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# Temporary folders
tmp/
temp/

# Vercel specific (not needed for Coolify)
.vercel

# Coolify specific ignores
backups/
*.backup
*.dump

# Development tools
.eslintrc.js
.prettierrc
.prettierignore
jest.config.js
cypress/
cypress.json

# Storybook
.storybook/
storybook-static/

# TypeScript
*.tsbuildinfo

# Sentry
.sentryclirc

# Local development
.env.example
.env.coolify.example
.env.production.example

# Scripts that shouldn't be in container
scripts/deploy-*.sh
scripts/backup-*.sh
scripts/validate-*.js

# Docker files (already in context)
Dockerfile*
docker-compose*.yml
.dockerignore

# Nginx config (handled separately)
nginx/

# Database files
*.sqlite
*.db

# Certificates
*.pem
*.key
*.crt

# Terraform
*.tfstate
*.tfstate.backup
.terraform/

# Kubernetes
*.yaml
*.yml
k8s/

# Monitoring
prometheus/
grafana/

# Misc
.cache/
.temp/
.tmp/

# =====================================================
# AGENTSALUD MVP - PRODUCTION ENVIRONMENT VARIABLES
# =====================================================
# Copy this file to .env.production and update with actual values
# 
# <AUTHOR> DevOps Team
# @date January 2025

# =====================================================
# ENVIRONMENT CONFIGURATION
# =====================================================
ENVIRONMENT=production
NODE_ENV=production
DEBUG=false
NEXT_TELEMETRY_DISABLED=1

# =====================================================
# APPLICATION URLS
# =====================================================
NEXT_PUBLIC_APP_URL=https://agentsalud.com
EVOLUTION_API_BASE_URL=https://evolution.agentsalud.com
WEBHOOK_GLOBAL_URL=https://agentsalud.com/api/whatsapp/webhook

# =====================================================
# SUPABASE CONFIGURATION (PRODUCTION)
# =====================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_production_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_production_supabase_service_role_key
SUPABASE_JWT_SECRET=your_production_supabase_jwt_secret

# =====================================================
# EVOLUTION API v2 CONFIGURATION
# =====================================================
EVOLUTION_API_KEY=your_production_evolution_api_key
EVOLUTION_WEBHOOK_VERIFY_TOKEN=your_production_webhook_verify_token

# WhatsApp Business Configuration
WHATSAPP_BUSINESS_ACCOUNT_ID=your_whatsapp_business_account_id
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token

# =====================================================
# OPENAI CONFIGURATION
# =====================================================
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# =====================================================
# AUTHENTICATION & SECURITY
# =====================================================
NEXTAUTH_SECRET=your_nextauth_secret_for_production
NEXTAUTH_URL=https://agentsalud.com

# JWT Configuration
JWT_SECRET=your_jwt_secret_for_production
JWT_EXPIRES_IN=24h

# Encryption Keys
ENCRYPTION_KEY=your_32_character_encryption_key
ENCRYPTION_IV=your_16_character_iv

# =====================================================
# MONITORING & LOGGING
# =====================================================
# Sentry Configuration
SENTRY_DSN=your_sentry_dsn_for_production
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=production-latest

# Vercel Analytics
VERCEL_ANALYTICS_ID=your_vercel_analytics_id

# =====================================================
# RATE LIMITING & PERFORMANCE
# =====================================================
# API Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# Performance Configuration
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT_MS=30000
KEEP_ALIVE_TIMEOUT_MS=5000

# =====================================================
# FEATURE FLAGS
# =====================================================
# Feature toggles for production environment
FEATURE_WHATSAPP_ENABLED=true
FEATURE_TELEGRAM_ENABLED=false
FEATURE_VOICE_ENABLED=false
FEATURE_AI_BOOKING_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_MAINTENANCE_MODE=false

# =====================================================
# COMPLIANCE & SECURITY
# =====================================================
# HIPAA Compliance
HIPAA_AUDIT_ENABLED=true
HIPAA_ENCRYPTION_ENABLED=true
HIPAA_LOG_RETENTION_DAYS=2555

# Security Headers
SECURITY_HEADERS_ENABLED=true
CORS_ORIGIN=https://agentsalud.com
CSP_ENABLED=true

# =====================================================
# SYSTEM LIMITS
# =====================================================
MAX_ORGANIZATIONS=1000
MAX_USERS_PER_ORG=500
SESSION_TIMEOUT=480
MAINTENANCE_MODE=false
REGISTRATION_ENABLED=true
EMAIL_NOTIFICATIONS=true
BACKUP_FREQUENCY=daily

# =====================================================
# DEPLOYMENT CONFIGURATION
# =====================================================
# Deployment metadata
DEPLOYMENT_VERSION=production-v1.0.0
DEPLOYMENT_DATE=2025-01-28
DEPLOYMENT_COMMIT_SHA=production-commit-sha
DEPLOYMENT_BRANCH=main

# =====================================================
# NOTES
# =====================================================
# 1. Replace all placeholder values with actual production credentials
# 2. Ensure all secrets are properly secured and rotated regularly
# 3. Use strong passwords and encryption keys
# 4. Configure monitoring alerts for all critical services
# 5. Test backup and recovery procedures regularly
# 6. Keep this file secure and never commit to version control

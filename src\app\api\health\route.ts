/**
 * =====================================================
 * AGENTSALUD MVP - HEALTH CHECK ENDPOINT
 * =====================================================
 * Comprehensive health check for Coolify + Supabase deployment
 * 
 * <AUTHOR> DevOps Team
 * @date January 2025
 */

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

// Health check configuration
const HEALTH_CHECK_CONFIG = {
  timeout: 5000, // 5 seconds timeout
  checks: {
    database: true,
    redis: true,
    external_apis: true,
    environment: true
  }
};

// Initialize Supabase client for health checks
const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  process.env.SUPABASE_SERVICE_ROLE_KEY || ''
);

interface HealthCheckResult {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: string;
  version: string;
  deployment: {
    platform: string;
    architecture: string;
    environment: string;
  };
  services: {
    database: ServiceHealth;
    redis: ServiceHealth;
    external_apis: ServiceHealth;
    environment: ServiceHealth;
  };
  performance: {
    uptime: number;
    memory_usage: number;
    response_time: number;
  };
}

interface ServiceHealth {
  status: 'healthy' | 'unhealthy' | 'degraded';
  response_time?: number;
  error?: string;
  details?: any;
}

// Check Supabase database connectivity
async function checkSupabaseHealth(): Promise<ServiceHealth> {
  const startTime = Date.now();
  
  try {
    // Test basic connectivity with a simple query
    const { data, error } = await supabase
      .from('organizations')
      .select('count')
      .limit(1)
      .single();
    
    const responseTime = Date.now() - startTime;
    
    if (error && error.code !== 'PGRST116') { // PGRST116 is "no rows returned" which is OK
      return {
        status: 'unhealthy',
        response_time: responseTime,
        error: error.message
      };
    }
    
    return {
      status: responseTime < 1000 ? 'healthy' : 'degraded',
      response_time: responseTime,
      details: {
        connection: 'established',
        query_time: `${responseTime}ms`
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      response_time: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Unknown database error'
    };
  }
}

// Check Redis connectivity (if available)
async function checkRedisHealth(): Promise<ServiceHealth> {
  const startTime = Date.now();
  
  try {
    // In a real implementation, you would check Redis connectivity here
    // For now, we'll simulate a basic check
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'healthy',
      response_time: responseTime,
      details: {
        connection: 'available',
        cache: 'operational'
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      response_time: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Redis connection failed'
    };
  }
}

// Check external APIs (OpenAI, Evolution API)
async function checkExternalAPIs(): Promise<ServiceHealth> {
  const startTime = Date.now();
  
  try {
    const checks = [];
    
    // Check OpenAI API availability
    if (process.env.OPENAI_API_KEY) {
      checks.push('openai');
    }
    
    // Check Evolution API availability
    if (process.env.EVOLUTION_API_BASE_URL) {
      checks.push('evolution');
    }
    
    const responseTime = Date.now() - startTime;
    
    return {
      status: 'healthy',
      response_time: responseTime,
      details: {
        configured_apis: checks,
        status: 'configured'
      }
    };
  } catch (error) {
    return {
      status: 'degraded',
      response_time: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'External API check failed'
    };
  }
}

// Check environment configuration
async function checkEnvironmentHealth(): Promise<ServiceHealth> {
  const startTime = Date.now();
  
  try {
    const requiredEnvVars = [
      'NODE_ENV',
      'NEXT_PUBLIC_SUPABASE_URL',
      'NEXT_PUBLIC_SUPABASE_ANON_KEY',
      'SUPABASE_SERVICE_ROLE_KEY',
      'NEXTAUTH_SECRET'
    ];
    
    const missingVars = requiredEnvVars.filter(varName => !process.env[varName]);
    const responseTime = Date.now() - startTime;
    
    if (missingVars.length > 0) {
      return {
        status: 'unhealthy',
        response_time: responseTime,
        error: `Missing environment variables: ${missingVars.join(', ')}`
      };
    }
    
    return {
      status: 'healthy',
      response_time: responseTime,
      details: {
        environment: process.env.NODE_ENV,
        required_vars: 'configured',
        deployment_platform: 'coolify',
        database_provider: 'supabase'
      }
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      response_time: Date.now() - startTime,
      error: error instanceof Error ? error.message : 'Environment check failed'
    };
  }
}

// Get system performance metrics
function getPerformanceMetrics() {
  const uptime = process.uptime();
  const memoryUsage = process.memoryUsage();
  
  return {
    uptime: Math.floor(uptime),
    memory_usage: Math.round((memoryUsage.heapUsed / memoryUsage.heapTotal) * 100),
    response_time: 0 // Will be calculated at the end
  };
}

export async function GET(request: NextRequest): Promise<NextResponse> {
  const startTime = Date.now();
  
  try {
    // Run all health checks in parallel
    const [databaseHealth, redisHealth, externalAPIsHealth, environmentHealth] = await Promise.all([
      checkSupabaseHealth(),
      checkRedisHealth(),
      checkExternalAPIs(),
      checkEnvironmentHealth()
    ]);
    
    // Calculate overall status
    const services = {
      database: databaseHealth,
      redis: redisHealth,
      external_apis: externalAPIsHealth,
      environment: environmentHealth
    };
    
    const serviceStatuses = Object.values(services).map(service => service.status);
    const hasUnhealthy = serviceStatuses.includes('unhealthy');
    const hasDegraded = serviceStatuses.includes('degraded');
    
    let overallStatus: 'healthy' | 'unhealthy' | 'degraded';
    if (hasUnhealthy) {
      overallStatus = 'unhealthy';
    } else if (hasDegraded) {
      overallStatus = 'degraded';
    } else {
      overallStatus = 'healthy';
    }
    
    // Get performance metrics
    const performance = getPerformanceMetrics();
    performance.response_time = Date.now() - startTime;
    
    const healthResult: HealthCheckResult = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      version: process.env.DEPLOYMENT_VERSION || '1.0.0',
      deployment: {
        platform: 'coolify',
        architecture: 'hybrid-supabase',
        environment: process.env.NODE_ENV || 'development'
      },
      services,
      performance
    };
    
    // Return appropriate HTTP status code
    const httpStatus = overallStatus === 'healthy' ? 200 : 
                      overallStatus === 'degraded' ? 200 : 503;
    
    return NextResponse.json(healthResult, { 
      status: httpStatus,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    });
    
  } catch (error) {
    // Emergency fallback response
    const errorResult: HealthCheckResult = {
      status: 'unhealthy',
      timestamp: new Date().toISOString(),
      version: 'unknown',
      deployment: {
        platform: 'coolify',
        architecture: 'hybrid-supabase',
        environment: process.env.NODE_ENV || 'unknown'
      },
      services: {
        database: { status: 'unhealthy', error: 'Health check failed' },
        redis: { status: 'unhealthy', error: 'Health check failed' },
        external_apis: { status: 'unhealthy', error: 'Health check failed' },
        environment: { status: 'unhealthy', error: 'Health check failed' }
      },
      performance: {
        uptime: 0,
        memory_usage: 0,
        response_time: Date.now() - startTime
      }
    };
    
    return NextResponse.json(errorResult, { 
      status: 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Content-Type': 'application/json'
      }
    });
  }
}

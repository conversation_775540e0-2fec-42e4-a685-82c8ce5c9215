{"version": 2, "name": "agentsalud-mvp", "alias": ["agentsalud.com", "www.agentsalud.com"], "regions": ["iad1", "sfo1"], "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm ci", "devCommand": "npm run dev", "env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1"}, "build": {"env": {"NODE_ENV": "production", "NEXT_TELEMETRY_DISABLED": "1", "SKIP_ENV_VALIDATION": "false"}}, "functions": {"src/app/api/**/*.ts": {"maxDuration": 30}, "src/app/api/webhooks/**/*.ts": {"maxDuration": 60}, "src/app/api/channels/whatsapp/instances/*/qrcode/stream/*.ts": {"maxDuration": 300}, "src/app/api/ai/**/*.ts": {"maxDuration": 60}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://agentsalud.com"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization, X-Requested-With"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/(.*)", "headers": [{"key": "Strict-Transport-Security", "value": "max-age=31536000; includeSubDomains; preload"}, {"key": "Content-Security-Policy", "value": "default-src 'self'; script-src 'self' 'unsafe-eval' 'unsafe-inline' https://vercel.live; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https://*.supabase.co https://api.openai.com wss://*.supabase.co; frame-ancestors 'none';"}]}], "rewrites": [{"source": "/api/webhooks/evolution/:orgId", "destination": "/api/webhooks/evolution/[orgId]"}], "redirects": [{"source": "/admin", "destination": "/dashboard", "permanent": false}], "crons": [{"path": "/api/admin/cleanup/sessions", "schedule": "0 2 * * *"}, {"path": "/api/admin/backup/database", "schedule": "0 3 * * *"}]}
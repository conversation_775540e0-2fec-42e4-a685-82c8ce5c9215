#!/usr/bin/env node

/**
 * Test URL Fix for Evolution API
 * 
 * Verifies that the URL construction fix resolves the double slash issue.
 * 
 * <AUTHOR> Development Team
 * @date 2025-01-28
 */

require('dotenv').config({ path: '.env.local' });

console.log('🧪 TESTING URL FIX FOR EVOLUTION API');
console.log('='.repeat(50));

// Simulate the fix
const baseUrlFromEnv = process.env.EVOLUTION_API_BASE_URL || 'https://evo.torrecentral.com';
const fixedBaseUrl = baseUrlFromEnv.replace(/\/$/, '');

console.log('\n🔧 URL Construction Test:');
console.log(`Original base URL: "${baseUrlFromEnv}"`);
console.log(`Fixed base URL: "${fixedBaseUrl}"`);

// Test URL construction
const createEndpoint = `${fixedBaseUrl}/instance/create`;
const connectEndpoint = `${fixedBaseUrl}/instance/connect/test-instance`;

console.log('\n📡 Endpoint URLs:');
console.log(`Create endpoint: ${createEndpoint}`);
console.log(`Connect endpoint: ${connectEndpoint}`);

// Verify no double slashes
const hasDoubleSlash = createEndpoint.includes('//') && !createEndpoint.startsWith('http');
console.log(`\n✅ Double slash check: ${hasDoubleSlash ? '❌ FAILED' : '✅ PASSED'}`);

async function testEvolutionAPI() {
  console.log('\n🚀 Testing Evolution API with Fixed URLs');
  console.log('-'.repeat(40));
  
  try {
    // Test create endpoint
    console.log('\n📋 Testing create endpoint...');
    const createPayload = {
      instanceName: `test-fix-${Date.now()}`,
      qrcode: true,
      integration: 'WHATSAPP-BAILEYS'
    };
    
    console.log(`URL: ${createEndpoint}`);
    console.log(`Payload:`, JSON.stringify(createPayload, null, 2));
    
    const createResponse = await fetch(createEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'apikey': process.env.EVOLUTION_API_KEY
      },
      body: JSON.stringify(createPayload)
    });
    
    console.log(`Response: ${createResponse.status} ${createResponse.statusText}`);
    
    if (createResponse.ok) {
      const result = await createResponse.json();
      console.log('✅ CREATE SUCCESS:', {
        instanceId: result.instance?.instanceId,
        status: result.instance?.status
      });
      
      // Test connect endpoint for QR
      if (result.instance?.instanceName) {
        console.log('\n📱 Testing QR endpoint...');
        const qrEndpoint = `${fixedBaseUrl}/instance/connect/${result.instance.instanceName}`;
        console.log(`QR URL: ${qrEndpoint}`);
        
        const qrResponse = await fetch(qrEndpoint, {
          headers: { 'apikey': process.env.EVOLUTION_API_KEY }
        });
        
        console.log(`QR Response: ${qrResponse.status} ${qrResponse.statusText}`);
        
        if (qrResponse.ok) {
          const qrData = await qrResponse.json();
          console.log('✅ QR SUCCESS:', {
            hasBase64: !!qrData.base64,
            base64Length: qrData.base64?.length || 0
          });
          return true;
        } else {
          const qrError = await qrResponse.text();
          console.log('❌ QR FAILED:', qrError);
        }
      }
      
      return true;
    } else {
      const errorText = await createResponse.text();
      console.log('❌ CREATE FAILED:', errorText);
      return false;
    }
    
  } catch (error) {
    console.error('❌ Test Error:', error.message);
    return false;
  }
}

async function runTest() {
  console.log('\n🚀 Starting URL Fix Test');
  console.log('='.repeat(50));
  
  const success = await testEvolutionAPI();
  
  console.log('\n' + '='.repeat(50));
  console.log('📊 TEST RESULTS');
  console.log('='.repeat(50));
  
  if (success) {
    console.log('\n🎉 SUCCESS: URL fix is working!');
    console.log('✅ No more double slash errors');
    console.log('✅ Evolution API endpoints accessible');
    console.log('✅ Instance creation working');
    console.log('✅ QR code generation working');
    
    console.log('\n📋 Next Steps:');
    console.log('1. The URL fix resolves the 500 error');
    console.log('2. Test the WhatsApp instance creation in browser');
    console.log('3. Verify QR code displays within 5 seconds');
    
  } else {
    console.log('\n🚨 FAILURE: URL fix needs more work');
    console.log('❌ Check Evolution API connectivity');
    console.log('❌ Verify API credentials');
    console.log('❌ Confirm endpoint URLs');
  }
  
  console.log('\n' + '='.repeat(50));
  console.log('✨ URL Fix Test Complete!');
  console.log('='.repeat(50));
  
  return success;
}

// Run test
runTest()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Test failed:', error);
    process.exit(1);
  });

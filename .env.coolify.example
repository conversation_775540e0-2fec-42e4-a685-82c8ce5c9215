# =====================================================
# AGENTSALUD MVP - COOLIFY ENVIRONMENT VARIABLES
# =====================================================
# Copy this file to .env and update with actual values
# 
# <AUTHOR> DevOps Team
# @date January 2025

# =====================================================
# APPLICATION CONFIGURATION
# =====================================================
NODE_ENV=production
NEXT_TELEMETRY_DISABLED=1
NEXT_PUBLIC_APP_URL=https://agentsalud.com

# =====================================================
# AUTHENTICATION & SECURITY
# =====================================================
NEXTAUTH_SECRET=your_nextauth_secret_32_characters_min
NEXTAUTH_URL=https://agentsalud.com
JWT_SECRET=your_jwt_secret_32_characters_minimum
ENCRYPTION_KEY=your_32_character_encryption_key_here
ENCRYPTION_IV=your_16_char_iv_here

# =====================================================
# DATABASE CONFIGURATION
# =====================================================
# PostgreSQL Configuration
POSTGRES_DB=agentsalud
POSTGRES_USER=agentsalud
POSTGRES_PASSWORD=your_secure_postgres_password
DATABASE_URL=*******************************************************************/agentsalud

# Supabase (if using external Supabase)
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
SUPABASE_JWT_SECRET=your_supabase_jwt_secret

# =====================================================
# REDIS CONFIGURATION
# =====================================================
REDIS_PASSWORD=your_secure_redis_password
REDIS_URL=redis://:your_secure_redis_password@redis:6379

# =====================================================
# EVOLUTION API CONFIGURATION
# =====================================================
EVOLUTION_API_BASE_URL=https://evolution.agentsalud.com
EVOLUTION_API_KEY=your_evolution_api_key
EVOLUTION_DATABASE_URL=*******************************************************************/agentsalud
WEBHOOK_GLOBAL_URL=https://agentsalud.com/api/webhooks/evolution

# WhatsApp Business Configuration
WHATSAPP_BUSINESS_ACCOUNT_ID=your_whatsapp_business_account_id
WHATSAPP_PHONE_NUMBER_ID=your_whatsapp_phone_number_id
WHATSAPP_ACCESS_TOKEN=your_whatsapp_access_token

# =====================================================
# OPENAI CONFIGURATION
# =====================================================
OPENAI_API_KEY=your_openai_api_key
OPENAI_MODEL=gpt-4
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# =====================================================
# MONITORING & LOGGING
# =====================================================
# Sentry Configuration
SENTRY_DSN=your_sentry_dsn_for_production
SENTRY_ENVIRONMENT=production
SENTRY_RELEASE=production-latest

# =====================================================
# FEATURE FLAGS
# =====================================================
FEATURE_WHATSAPP_ENABLED=true
FEATURE_TELEGRAM_ENABLED=false
FEATURE_VOICE_ENABLED=false
FEATURE_AI_BOOKING_ENABLED=true
FEATURE_ANALYTICS_ENABLED=true
FEATURE_MAINTENANCE_MODE=false

# =====================================================
# SECURITY & COMPLIANCE
# =====================================================
# HIPAA Compliance
HIPAA_AUDIT_ENABLED=true
HIPAA_ENCRYPTION_ENABLED=true
HIPAA_LOG_RETENTION_DAYS=2555

# Security Headers
SECURITY_HEADERS_ENABLED=true
CORS_ORIGIN=https://agentsalud.com
CSP_ENABLED=true

# =====================================================
# PERFORMANCE & LIMITS
# =====================================================
# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESSFUL_REQUESTS=true

# System Limits
MAX_ORGANIZATIONS=1000
MAX_USERS_PER_ORG=500
SESSION_TIMEOUT=480
MAX_CONCURRENT_REQUESTS=100
REQUEST_TIMEOUT_MS=30000

# =====================================================
# BACKUP & MAINTENANCE
# =====================================================
BACKUP_FREQUENCY=daily
BACKUP_RETENTION_DAYS=30
MAINTENANCE_MODE=false
REGISTRATION_ENABLED=true
EMAIL_NOTIFICATIONS=true

# =====================================================
# COOLIFY SPECIFIC
# =====================================================
# Coolify Configuration
COOLIFY_APP_NAME=agentsalud-mvp
COOLIFY_DOMAIN=agentsalud.com
COOLIFY_PORT=3000
COOLIFY_HEALTH_CHECK_PATH=/api/health

# SSL Configuration
SSL_ENABLED=true
SSL_REDIRECT=true
FORCE_HTTPS=true

# =====================================================
# DEPLOYMENT METADATA
# =====================================================
DEPLOYMENT_VERSION=coolify-v1.0.0
DEPLOYMENT_DATE=2025-01-28
DEPLOYMENT_PLATFORM=coolify
DEPLOYMENT_ENVIRONMENT=production
